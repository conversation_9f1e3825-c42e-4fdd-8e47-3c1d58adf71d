#!/usr/bin/env node

/**
 * Generate PWA icons from the existing logo
 * This script creates the necessary icon files for the PWA manifest
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Icon sizes needed for PWA
const iconSizes = [
    { size: 72, name: 'icon-72x72.png' },
    { size: 96, name: 'icon-96x96.png' },
    { size: 128, name: 'icon-128x128.png' },
    { size: 144, name: 'icon-144x144.png' },
    { size: 152, name: 'icon-152x152.png' },
    { size: 192, name: 'icon-192x192.png' },
    { size: 384, name: 'icon-384x384.png' },
    { size: 512, name: 'icon-512x512.png' }
];

// Create build/assets directory if it doesn't exist
const assetsDir = path.join(__dirname, 'public', 'build', 'assets');
if (!fs.existsSync(assetsDir)) {
    fs.mkdirSync(assetsDir, { recursive: true });
}

// Check if ImageMagick or similar is available
function checkImageMagick() {
    try {
        // Try the newer 'magick' command first, then fall back to 'convert'
        execSync('magick -version', { stdio: 'ignore' });
        return 'magick';
    } catch (error) {
        try {
            execSync('convert -version', { stdio: 'ignore' });
            return 'convert';
        } catch (error2) {
            return false;
        }
    }
}

// Generate icons using ImageMagick
function generateIconsWithImageMagick(command) {
    const logoPath = path.join(__dirname, 'public', 'images', 'logo.png');

    if (!fs.existsSync(logoPath)) {
        console.error('❌ Logo file not found at:', logoPath);
        return false;
    }

    console.log('🎨 Generating PWA icons using ImageMagick...');

    iconSizes.forEach(({ size, name }) => {
        const outputPath = path.join(assetsDir, name);
        try {
            if (command === 'magick') {
                execSync(`magick "${logoPath}" -resize ${size}x${size} "${outputPath}"`);
            } else {
                execSync(`convert "${logoPath}" -resize ${size}x${size} "${outputPath}"`);
            }
            console.log(`✅ Generated ${name}`);
        } catch (error) {
            console.error(`❌ Failed to generate ${name}:`, error.message);
        }
    });

    return true;
}

// Generate a simple placeholder icon using Canvas (fallback)
function generatePlaceholderIcons() {
    console.log('📝 Generating placeholder PWA icons...');
    
    // Create simple SVG icons that can be converted to PNG
    const svgTemplate = (size) => `
<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#2563eb"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${size/8}" 
        fill="white" text-anchor="middle" dominant-baseline="middle">
    عمارتنا - Amaretna
  </text>
</svg>`;

    iconSizes.forEach(({ size, name }) => {
        const svgContent = svgTemplate(size);
        const svgPath = path.join(assetsDir, name.replace('.png', '.svg'));
        
        try {
            fs.writeFileSync(svgPath, svgContent);
            console.log(`✅ Generated placeholder ${name.replace('.png', '.svg')}`);
        } catch (error) {
            console.error(`❌ Failed to generate ${name}:`, error.message);
        }
    });
    
    console.log('ℹ️  Note: SVG icons generated. For better PWA support, convert to PNG using:');
    console.log('   npm install -g svg2png-cli');
    console.log('   svg2png public/build/assets/*.svg');
}

// Main execution
function main() {
    console.log('🚀 Starting PWA icon generation...');

    const magickCommand = checkImageMagick();
    if (magickCommand) {
        if (generateIconsWithImageMagick(magickCommand)) {
            console.log('✅ PWA icons generated successfully!');
            return;
        }
    }

    console.log('⚠️  ImageMagick not found, generating placeholder icons...');
    generatePlaceholderIcons();

    console.log('\n📋 Next steps:');
    console.log('1. Install ImageMagick: brew install imagemagick (macOS) or apt-get install imagemagick (Ubuntu)');
    console.log('2. Run this script again to generate proper PNG icons');
    console.log('3. Or manually create PNG icons from the generated SVG files');
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

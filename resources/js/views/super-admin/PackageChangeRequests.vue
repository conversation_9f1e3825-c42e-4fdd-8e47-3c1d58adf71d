<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('package_change_requests') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('package_change_requests_description') }}</p>
          </div>
          <div class="mt-4 sm:mt-0">
            <div class="flex items-center space-x-3">
              <div class="text-sm text-gray-500">
                <span class="font-medium">{{ $t('total_requests') }}:</span> {{ statistics.total || 0 }}
              </div>
              <div class="text-sm text-gray-500">
                <span class="font-medium">{{ $t('pending') }}:</span> {{ statistics.pending || 0 }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('search') }}</label>
            <input
              v-model="filters.search"
              @input="debounceSearch"
              type="text"
              :placeholder="$t('search_requests')"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('status') }}</label>
            <select
              v-model="filters.status"
              @change="loadRequests"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option :value="null">{{ $t('all_statuses') }}</option>
              <option value="pending">{{ $t('pending') }}</option>
              <option value="approved">{{ $t('approved') }}</option>
              <option value="rejected">{{ $t('rejected') }}</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('billing_cycle') }}</label>
            <select
              v-model="filters.billing_cycle"
              @change="loadRequests"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option :value="null">{{ $t('all_cycles') }}</option>
              <option value="monthly">{{ $t('monthly') }}</option>
              <option value="annual">{{ $t('annual') }}</option>
            </select>
          </div>

          <div class="flex items-end">
            <button
              @click="clearFilters"
              class="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              {{ $t('clear_filters') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Requests Table -->
      <div v-if="loading" class="flex justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="requests.length === 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        <h3 class="mt-4 text-lg font-medium text-gray-900">{{ $t('no_requests_found') }}</h3>
        <p class="mt-2 text-sm text-gray-500">{{ $t('no_requests_found_description') }}</p>
      </div>

      <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('building') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('requested_by') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('current_package') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('requested_package') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('billing_cycle') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('status') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('date') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ $t('actions') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="request in requests" :key="request.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ request.building?.name }}</div>
                  <div class="text-sm text-gray-500">{{ request.building?.address }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ request.requested_by?.name }}</div>
                  <div class="text-sm text-gray-500">{{ request.requested_by?.email }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ request.current_package?.name || $t('no_package') }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ request.requested_package?.name }}</div>
                  <div class="text-sm text-gray-500">{{ request.requested_package?.price_formatted }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                        :class="request.billing_cycle === 'annual' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'">
                    {{ $t(request.billing_cycle) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                        :class="getStatusClass(request.status)">
                    {{ $t(request.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatDate(request.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex items-center space-x-2">
                    <button
                      @click="viewRequest(request)"
                      class="text-blue-600 hover:text-blue-900"
                      :title="$t('view_details')"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                      </svg>
                    </button>
                    <button
                      v-if="request.status === 'pending'"
                      @click="approveRequest(request)"
                      class="text-green-600 hover:text-green-900"
                      :title="$t('approve_request')"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                      </svg>
                    </button>
                    <button
                      v-if="request.status === 'pending'"
                      @click="rejectRequest(request)"
                      class="text-red-600 hover:text-red-900"
                      :title="$t('reject_request')"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="pagination" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              v-if="pagination.prev_page_url"
              @click="loadRequests(pagination.current_page - 1)"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              {{ $t('previous') }}
            </button>
            <button
              v-if="pagination.next_page_url"
              @click="loadRequests(pagination.current_page + 1)"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              {{ $t('next') }}
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                {{ $t('showing') }}
                <span class="font-medium">{{ pagination.from }}</span>
                {{ $t('to') }}
                <span class="font-medium">{{ pagination.to }}</span>
                {{ $t('of') }}
                <span class="font-medium">{{ pagination.total }}</span>
                {{ $t('results') }}
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  v-if="pagination.prev_page_url"
                  @click="loadRequests(pagination.current_page - 1)"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  <span class="sr-only">{{ $t('previous') }}</span>
                  <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                </button>
                <button
                  v-if="pagination.next_page_url"
                  @click="loadRequests(pagination.current_page + 1)"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                >
                  <span class="sr-only">{{ $t('next') }}</span>
                  <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Request Details Modal -->
    <div v-if="showDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">{{ $t('request_details') }}</h3>
            <button
              @click="closeDetailsModal"
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>

          <div v-if="selectedRequest" class="space-y-4">
            <!-- Building Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">{{ $t('building_information') }}</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('building_name') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ selectedRequest.building?.name }}</span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('building_address') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ selectedRequest.building?.address }}</span>
                </div>
              </div>
            </div>

            <!-- Request Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">{{ $t('request_information') }}</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('requested_by') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ selectedRequest.requested_by?.name }}</span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('request_date') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ formatDate(selectedRequest.created_at) }}</span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('billing_cycle') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ $t(selectedRequest.billing_cycle) }}</span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('payment_method') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ $t(selectedRequest.payment_method) }}</span>
                </div>
              </div>
            </div>

            <!-- Package Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">{{ $t('package_information') }}</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('current_package') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ selectedRequest.current_package?.name || $t('no_package') }}</span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('requested_package') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ selectedRequest.requested_package?.name }}</span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('current_price') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ selectedRequest.current_package?.price_formatted || $t('free') }}</span>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('requested_price') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ selectedRequest.requested_package?.price_formatted }}</span>
                </div>
              </div>
            </div>

            <!-- Banking Information (for Bank Transfer) -->
            <div v-if="selectedRequest.payment_method === 'bank_transfer'" class="bg-blue-50 border border-blue-200 p-4 rounded-lg">
              <h4 class="font-medium text-blue-900 mb-3 flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                {{ $t('banking_information') }}
              </h4>
              <div class="bg-white p-4 rounded-md border border-blue-200">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span class="text-sm font-medium text-gray-600">{{ $t('bank_name') }}:</span>
                    <p class="text-sm text-gray-900 font-medium mt-1">Arab Bank</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-600">{{ $t('account_holder') }}:</span>
                    <p class="text-sm text-gray-900 font-medium mt-1">Taymoor Qanadilou</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-600">iBuraq:</span>
                    <p class="text-sm text-gray-900 font-mono font-medium mt-1">**********</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-600">IBAN:</span>
                    <p class="text-sm text-gray-900 font-mono font-medium mt-1">*****************************</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-600">{{ $t('swift_code') }}:</span>
                    <p class="text-sm text-gray-900 font-mono font-medium mt-1">ARABPS22XXX</p>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-600">{{ $t('bank_address') }}:</span>
                    <p class="text-sm text-gray-900 mt-1">Nablus Branch<br>Palestine</p>
                  </div>
                </div>
                <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div class="flex items-start">
                    <svg class="w-5 h-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                      <h5 class="text-sm font-medium text-yellow-800">{{ $t('payment_instructions') }}</h5>
                      <p class="text-sm text-yellow-700 mt-1">
                        {{ $t('bank_transfer_instructions') }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Reason -->
            <div v-if="selectedRequest.reason" class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">{{ $t('reason') }}</h4>
              <p class="text-sm text-gray-900">{{ selectedRequest.reason }}</p>
            </div>

            <!-- Admin Notes -->
            <div v-if="selectedRequest.admin_notes" class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">{{ $t('admin_notes') }}</h4>
              <p class="text-sm text-gray-900">{{ selectedRequest.admin_notes }}</p>
            </div>

            <!-- Status Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">{{ $t('status_information') }}</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span class="text-sm font-medium text-gray-500">{{ $t('status') }}:</span>
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-2"
                        :class="getStatusClass(selectedRequest.status)">
                    {{ $t(selectedRequest.status) }}
                  </span>
                </div>
                <div v-if="selectedRequest.approved_by">
                  <span class="text-sm font-medium text-gray-500">{{ $t('approved_by') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ selectedRequest.approved_by?.name }}</span>
                </div>
                <div v-if="selectedRequest.approved_at">
                  <span class="text-sm font-medium text-gray-500">{{ $t('approved_at') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ formatDate(selectedRequest.approved_at) }}</span>
                </div>
                <div v-if="selectedRequest.rejected_at">
                  <span class="text-sm font-medium text-gray-500">{{ $t('rejected_at') }}:</span>
                  <span class="text-sm text-gray-900 ml-2">{{ formatDate(selectedRequest.rejected_at) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div v-if="selectedRequest?.status === 'pending'" class="flex justify-end space-x-3 mt-6">
            <button
              @click="approveRequest(selectedRequest)"
              class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors"
            >
              {{ $t('approve') }}
            </button>
            <button
              @click="rejectRequest(selectedRequest)"
              class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors"
            >
              {{ $t('reject') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Reject Modal -->
    <div v-if="showRejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('reject_request') }}</h3>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('rejection_reason') }}</label>
            <textarea
              v-model="rejectionNotes"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              :placeholder="$t('rejection_reason_placeholder')"
            ></textarea>
          </div>
          <div class="flex justify-end space-x-3">
            <button
              @click="closeRejectModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              {{ $t('cancel') }}
            </button>
            <button
              @click="confirmReject"
              class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors"
            >
              {{ $t('reject') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PackageChangeRequests',
  data() {
    return {
      requests: [],
      loading: false,
      filters: {
        search: '',
        status: null,
        billing_cycle: null,
      },
      pagination: null,
      statistics: {},
      showDetailsModal: false,
      showRejectModal: false,
      selectedRequest: null,
      rejectionNotes: '',
      searchTimeout: null,
    };
  },
  mounted() {
    this.loadRequests();
    this.loadStatistics();
  },
  methods: {
    async loadRequests(page = 1) {
      this.loading = true;
      try {
        const params = {
          page,
          ...this.filters,
        };

        const response = await this.$axios.get('/super-admin/package-approvals', { params });
        this.requests = response.data.data;
        this.pagination = {
          current_page: response.data.current_page,
          last_page: response.data.last_page,
          per_page: response.data.per_page,
          total: response.data.total,
          from: response.data.from,
          to: response.data.to,
          prev_page_url: response.data.prev_page_url,
          next_page_url: response.data.next_page_url,
        };
      } catch (error) {
        console.error('Error loading requests:', error);
        this.$toast.error(this.$t('error_loading_requests'));
      } finally {
        this.loading = false;
      }
    },

    async loadStatistics() {
      try {
        const response = await this.$axios.get('/super-admin/package-approvals/statistics');
        this.statistics = response.data;
      } catch (error) {
        console.error('Error loading statistics:', error);
      }
    },

    debounceSearch() {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        this.loadRequests();
      }, 500);
    },

    clearFilters() {
      this.filters = {
        search: '',
        status: null,
        billing_cycle: null,
      };
      this.loadRequests();
    },

    viewRequest(request) {
      this.selectedRequest = request;
      this.showDetailsModal = true;
    },

    closeDetailsModal() {
      this.showDetailsModal = false;
      this.selectedRequest = null;
    },

    async approveRequest(request) {
      try {
        await this.$axios.post(`/super-admin/package-approvals/${request.id}/approve`);
        this.$toast.success(this.$t('request_approved_successfully'));
        this.loadRequests();
        this.loadStatistics();
        this.closeDetailsModal();
      } catch (error) {
        console.error('Error approving request:', error);
        this.$toast.error(this.$t('error_approving_request'));
      }
    },

    rejectRequest(request) {
      this.selectedRequest = request;
      this.rejectionNotes = '';
      this.showRejectModal = true;
    },

    closeRejectModal() {
      this.showRejectModal = false;
      this.selectedRequest = null;
      this.rejectionNotes = '';
    },

    async confirmReject() {
      try {
        await this.$axios.post(`/super-admin/package-approvals/${this.selectedRequest.id}/reject`, {
          admin_notes: this.rejectionNotes,
        });
        this.$toast.success(this.$t('request_rejected_successfully'));
        this.loadRequests();
        this.loadStatistics();
        this.closeRejectModal();
      } catch (error) {
        console.error('Error rejecting request:', error);
        this.$toast.error(this.$t('error_rejecting_request'));
      }
    },

    getStatusClass(status) {
      const classes = {
        pending: 'bg-yellow-100 text-yellow-800',
        approved: 'bg-green-100 text-green-800',
        rejected: 'bg-red-100 text-red-800',
      };
      return classes[status] || 'bg-gray-100 text-gray-800';
    },

    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString(this.$locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    },
  },
};
</script> 
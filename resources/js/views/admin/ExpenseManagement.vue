<template>
  <div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $t('expense_management') }}</h1>
            <p class="mt-1 text-sm text-gray-500">{{ $t('manage_building_expenses') }}</p>
          </div>
          <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            <router-link to="/admin/expenses/create"
              class="inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_expense') }}
            </router-link>
            <button @click="generateMonthlyExpenses" :disabled="generatingExpenses"
              class="inline-flex items-center justify-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
              <svg v-if="generatingExpenses" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
              <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8" />
              </svg>
              {{ generatingExpenses ? $t('generating') : $t('generate_monthly_expenses') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Page Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('total_expenses') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ totalExpenses.toFixed(2) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('this_month') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ thisMonthExpenses.toFixed(2) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('automatic') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ automaticExpenses.toFixed(2) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <p class="text-sm font-medium text-gray-600">{{ $t('manual') }}</p>
              <p class="text-2xl font-bold text-gray-900">{{ manualExpenses.toFixed(2) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters Section -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">{{ $t('filters') }}</h2>
          <p class="mt-1 text-sm text-gray-500">{{ $t('filter_expenses_description') }}</p>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('expense_type') }}</label>
              <div class="relative">
                <select v-model="filters.type" :class="[
                  'w-full py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
                  $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
                ]">
                  <option value="">{{ $t('all_types') }}</option>
                  <option value="building_services">{{ $t('building_services') }}</option>
                  <option value="building_electricity">{{ $t('building_electricity') }}</option>
                  <option value="personal_electricity">{{ $t('personal_electricity') }}</option>
                  <option value="water">{{ $t('water') }}</option>
                  <option value="other">{{ $t('other') }}</option>
                </select>
                <div :class="[
                  'absolute inset-y-0 flex items-center pointer-events-none',
                  $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
                ]">
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('month') }}</label>
              <div class="relative">
                <select v-model="filters.month" :class="[
                  'w-full py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white',
                  $isRTL() ? 'pr-3 pl-10' : 'pl-3 pr-10'
                ]">
                  <option value="">{{ $t('all_months') }}</option>
                  <option value="01">{{ $t('january') }}</option>
                  <option value="02">{{ $t('february') }}</option>
                  <option value="03">{{ $t('march') }}</option>
                  <option value="04">{{ $t('april') }}</option>
                  <option value="05">{{ $t('may') }}</option>
                  <option value="06">{{ $t('june') }}</option>
                  <option value="07">{{ $t('july') }}</option>
                  <option value="08">{{ $t('august') }}</option>
                  <option value="09">{{ $t('september') }}</option>
                  <option value="10">{{ $t('october') }}</option>
                  <option value="11">{{ $t('november') }}</option>
                  <option value="12">{{ $t('december') }}</option>
                </select>
                <div :class="[
                  'absolute inset-y-0 flex items-center pointer-events-none',
                  $isRTL() ? 'left-0 pl-2' : 'right-0 pr-2'
                ]">
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('year') }}</label>
              <input type="number" v-model="filters.year"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                min="2023" max="2030" placeholder="2025" />
            </div>

            <div class="flex items-end">
              <button @click="applyFilters"
                class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                {{ $t('apply_filters') }}
              </button>
            </div>
          </div>

          <!-- Active Filters Display -->
          <div v-if="hasActiveFilters" class="mt-4 flex flex-wrap gap-2">
            <span class="text-sm text-gray-500">{{ $t('active_filters') }}:</span>
            <span v-if="filters.type"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {{ $t('type') }}: {{ getTypeLabel(filters.type) }}
              <button @click="filters.type = ''; applyFilters()" class="ml-1 text-blue-600 hover:text-blue-800">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </span>
            <span v-if="filters.month"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {{ $t('month') }}: {{ getMonthLabel(filters.month) }}
              <button @click="filters.month = ''; applyFilters()" class="ml-1 text-green-600 hover:text-green-800">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </span>
            <span v-if="filters.year && filters.year !== new Date().getFullYear()"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              {{ $t('year') }}: {{ filters.year }}
              <button @click="filters.year = new Date().getFullYear(); applyFilters()"
                class="ml-1 text-purple-600 hover:text-purple-800">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </span>
            <button @click="clearAllFilters" class="text-sm text-red-600 hover:text-red-800 font-medium">
              {{ $t('clear_all_filters') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Expenses Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h2 class="text-lg font-medium text-gray-900">{{ $t('expense_records') }}</h2>
              <p class="mt-1 text-sm text-gray-500">{{ $t('expense_records_description') }}</p>
            </div>
            <div class="mt-4 sm:mt-0">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {{ pagination.total }} {{ $t('total_records') }}
              </span>
            </div>
          </div>
        </div>

        <data-table :columns="columns" :items="expenses" :loading="loading" :show-header="false">
          <template #actions="{ item }">
            <div class="flex items-center space-x-2">
              <button @click="viewExpense(item)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                {{ $t('view') }}
              </button>
              <button @click="editExpense(item)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100 transition-colors">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                {{ $t('edit') }}
              </button>
              <button @click="deleteExpense(item)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                {{ $t('delete') }}
              </button>
            </div>
          </template>
        </data-table>

        <!-- Pagination -->
        <div v-if="pagination.total > 0" class="px-6 py-4 border-t border-gray-200">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="text-sm text-gray-700">
              {{ $t('showing') }}
              <span class="font-medium">{{ pagination.from }}</span>
              {{ $t('to') }}
              <span class="font-medium">{{ pagination.to }}</span>
              {{ $t('of') }}
              <span class="font-medium">{{ pagination.total }}</span>
              {{ $t('results') }}
            </div>

            <div class="flex items-center space-x-2">
              <button @click="previousPage" :disabled="pagination.currentPage === 1"
                class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                {{ $t('previous') }}
              </button>

              <!-- Page Numbers -->
              <div class="hidden sm:flex items-center space-x-1">
                <template v-for="page in visiblePages" :key="page">
                  <button v-if="page !== '...'" @click="goToPage(page)" :class="[
                    'px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    page === pagination.currentPage
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                  ]">
                    {{ page }}
                  </button>
                  <span v-else class="px-2 py-2 text-sm text-gray-500">...</span>
                </template>
              </div>

              <button @click="nextPage" :disabled="pagination.currentPage === pagination.lastPage"
                class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                {{ $t('next') }}
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!loading" class="px-6 py-12 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $t('no_expenses_found') }}</h3>
          <p class="mt-1 text-sm text-gray-500">{{ $t('no_expenses_description') }}</p>
          <div class="mt-6">
            <router-link to="/admin/expenses/create"
              class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              {{ $t('create_first_expense') }}
            </router-link>
          </div>
        </div>
      </div>



      <!-- Notifications -->
      <notification :show="showNotification" :type="notificationType" :title="notificationTitle"
        :message="notificationMessage" @close="closeNotification" />
    </div>
  </div>
</template>

<script>
import DataTable from '../../components/DataTable.vue';
import ExpenseForm from '../../components/ExpenseForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DataTable,
    ExpenseForm,
    Notification
  },
  data() {
    return {
      loading: false,
      generatingExpenses: false,
      expenses: [],
      expenseTotals: {
        totalExpenses: 0,
        thisMonthExpenses: 0,
        automaticExpenses: 0,
        manualExpenses: 0
      },
      loadingTotals: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      user: null,
      isSuperAdmin: false,
      userLoaded: false,
      filters: {
        type: '',
        month: '',
        year: new Date().getFullYear()
      },
      pagination: {
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 0,
        total: 0
      },
      columns: []
    };
  },
  computed: {
    totalExpenses() {
      return this.expenseTotals.totalExpenses;
    },
    thisMonthExpenses() {
      return this.expenseTotals.thisMonthExpenses;
    },
    automaticExpenses() {
      return this.expenseTotals.automaticExpenses;
    },
    manualExpenses() {
      return this.expenseTotals.manualExpenses;
    },
    hasActiveFilters() {
      return this.filters.type ||
        this.filters.month ||
        (this.filters.year && this.filters.year !== new Date().getFullYear());
    },
    visiblePages() {
      const current = this.pagination.currentPage;
      const last = this.pagination.lastPage;
      const pages = [];

      if (last <= 7) {
        // Show all pages if 7 or fewer
        for (let i = 1; i <= last; i++) {
          pages.push(i);
        }
      } else {
        // Always show first page
        pages.push(1);

        if (current > 4) {
          pages.push('...');
        }

        // Show pages around current
        const start = Math.max(2, current - 1);
        const end = Math.min(last - 1, current + 1);

        for (let i = start; i <= end; i++) {
          pages.push(i);
        }

        if (current < last - 3) {
          pages.push('...');
        }

        // Always show last page
        if (last > 1) {
          pages.push(last);
        }
      }

      return pages;
    }
  },
  async created() {
    this.initializeColumns();
    this.initializeUserFromStorage();
    await this.fetchUser();
    await this.loadExpenseTypes();
    this.loadExpenses();
    this.loadExpenseTotals();
  },
  methods: {
    initializeColumns() {
      this.columns = [
        { key: 'expense_type.name', label: this.$t('type') },
        { key: 'user.name', label: this.$t('neighbor') },
        { key: 'user.apartment_number', label: this.$t('apartment') },
        { key: 'month', label: this.$t('month') },
        { key: 'year', label: this.$t('year') },
        { key: 'amount', label: this.$t('amount') },
        { key: 'is_automatic', label: this.$t('auto') },
        { key: 'notes', label: this.$t('notes') }
      ];
    },
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.userLoaded = true;
        localStorage.setItem('user', JSON.stringify(this.user));
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    },
    async loadExpenseTypes() {
      try {
        const response = await this.$axios.get('/expense-types');
        this.expenseTypes = response.data;
      } catch (error) {
        console.error('Error loading expense types:', error);
      }
    },
    async loadExpenses() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/expenses', {
          params: {
            page: this.pagination.currentPage,
            ...this.filters
          }
        });

        this.expenses = response.data.data;
        this.pagination = {
          currentPage: response.data.current_page,
          lastPage: response.data.last_page,
          from: response.data.from,
          to: response.data.to,
          total: response.data.total
        };
      } catch (error) {
        this.showError('Error loading expenses');
      } finally {
        this.loading = false;
      }
    },
    async loadExpenseTotals() {
      this.loadingTotals = true;
      try {
        // Use the new dedicated totals endpoint
        const response = await this.$axios.get('/expenses/totals', {
          params: this.filters
        });

        this.expenseTotals = response.data;
      } catch (error) {
        console.error('Error loading expense totals:', error);
        this.showError('Error loading expense totals');
      } finally {
        this.loadingTotals = false;
      }
    },
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadExpenses();
      this.loadExpenseTotals();
    },
    previousPage() {
      if (this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
        this.loadExpenses();
      }
    },
    nextPage() {
      if (this.pagination.currentPage < this.pagination.lastPage) {
        this.pagination.currentPage++;
        this.loadExpenses();
      }
    },
    viewExpense(expense) {
      this.$router.push(`/admin/expenses/${expense.id}`);
    },
    editExpense(expense) {
      this.$router.push(`/admin/expenses/${expense.id}/edit`);
    },
    async deleteExpense(expense) {
      if (confirm('Are you sure you want to delete this expense?')) {
        try {
          await this.$axios.delete(`/expenses/${expense.id}`);
          this.showSuccess('Deleted', 'Expense deleted successfully');
          this.loadExpenses();
          this.loadExpenseTotals();
        } catch (error) {
          this.showError('Delete Failed', 'Failed to delete expense');
        }
      }
    },

    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    },
    getTypeLabel(type) {
      const typeLabels = {
        'building_services': this.$t('building_services'),
        'building_electricity': this.$t('building_electricity'),
        'personal_electricity': this.$t('personal_electricity'),
        'water': this.$t('water'),
        'other': this.$t('other')
      };
      return typeLabels[type] || type;
    },
    getMonthLabel(month) {
      const monthLabels = {
        '01': this.$t('january'),
        '02': this.$t('february'),
        '03': this.$t('march'),
        '04': this.$t('april'),
        '05': this.$t('may'),
        '06': this.$t('june'),
        '07': this.$t('july'),
        '08': this.$t('august'),
        '09': this.$t('september'),
        '10': this.$t('october'),
        '11': this.$t('november'),
        '12': this.$t('december')
      };
      return monthLabels[month] || month;
    },
    clearAllFilters() {
      this.filters = {
        type: '',
        month: '',
        year: new Date().getFullYear()
      };
      this.applyFilters();
    },
    goToPage(page) {
      this.pagination.currentPage = page;
      this.loadExpenses();
    },
    async generateMonthlyExpenses() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      const formattedMonth = currentMonth.toString().padStart(2, '0');

      // Get building info to show the correct monthly fee
      let monthlyFee = 'dynamic amount';
      try {
        if (!this.isSuperAdmin) {
          const buildingResponse = await this.$axios.get('/my-building');
          monthlyFee = `${parseFloat(buildingResponse.data.monthly_fee).toFixed(2)}`;
        }
      } catch (error) {
        console.warn('Could not fetch building info for monthly fee display');
      }

      const confirmMessage = this.isSuperAdmin
        ? `Generate monthly expenses for all neighbors for ${formattedMonth}/${currentYear}?`
        : `Generate monthly expenses (${monthlyFee}) for all neighbors in your building for ${formattedMonth}/${currentYear}?`;

      if (confirm(confirmMessage)) {
        this.generatingExpenses = true;
        try {
          await this.$axios.post('/expenses/generate-monthly', {
            month: formattedMonth,
            year: currentYear.toString()
          });

          this.showSuccess('Generated', 'Monthly expenses generated successfully');
          this.loadExpenses();
          this.loadExpenseTotals();
        } catch (error) {
          this.showError('Generation Failed', error.response?.data?.message || 'Failed to generate monthly expenses');
        } finally {
          this.generatingExpenses = false;
        }
      }
    },
    async generateMonthlySubscriptions() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      const formattedMonth = currentMonth.toString().padStart(2, '0');

      // Get building info to show the correct subscription fee
      let subscriptionFee = 'dynamic amount';
      try {
        if (!this.isSuperAdmin) {
          const buildingResponse = await this.$axios.get('/my-building');
        }
      } catch (error) {
        console.warn('Could not fetch building info for subscription fee display');
      }

      const confirmMessage = this.isSuperAdmin
        ? `Generate monthly subscription expenses for all buildings for ${formattedMonth}/${currentYear}?`
        : `Generate monthly subscription expense (${subscriptionFee}) for your building for ${formattedMonth}/${currentYear}?`;

      if (confirm(confirmMessage)) {
        this.generatingExpenses = true;
        try {
          // Call the new subscription generation command
          await this.$axios.post('/subscriptions/generate-monthly', {
            month: formattedMonth,
            year: currentYear.toString()
          });

          this.showSuccess('Generated', 'Monthly subscription expenses generated successfully');
          this.loadExpenses();
          this.loadExpenseTotals();
        } catch (error) {
          this.showError('Generation Failed', error.response?.data?.message || 'Failed to generate monthly subscription expenses');
        } finally {
          this.generatingExpenses = false;
        }
      }
    }
  }
};
</script>
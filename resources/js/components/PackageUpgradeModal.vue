<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="handleOutsideClick">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white" @click.stop>
      <!-- Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">{{ $t('upgrade_package') }}</h3>
        <button @click="handleCancel" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="py-8">
        <div class="animate-pulse space-y-4">
          <div class="grid grid-cols-3 gap-4">
            <div class="h-32 bg-gray-200 rounded"></div>
            <div class="h-32 bg-gray-200 rounded"></div>
            <div class="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>

      <!-- Package Selection -->
      <div v-else-if="availablePackages.length > 0" class="py-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          <div 
            v-for="pkg in availablePackages" 
            :key="pkg.id"
            class="relative border rounded-lg p-6 cursor-pointer transition-all duration-200"
            :class="[
              form.selectedPackage?.id === pkg.id ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300',
              pkg.is_current ? 'opacity-50 cursor-not-allowed' : '',
              pkg.is_popular ? 'ring-2 ring-indigo-500' : ''
            ]"
            @click="!pkg.is_current && selectPackage(pkg)"
          >
            <!-- Popular Badge -->
            <div v-if="pkg.is_popular" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                {{ $t('popular') }}
              </span>
            </div>

            <!-- Current Badge -->
            <div v-if="pkg.is_current" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                {{ $t('current') }}
              </span>
            </div>

            <div class="text-center">
              <h4 class="text-lg font-semibold text-gray-900 mb-2">{{ pkg.name }}</h4>
              <div class="mb-4">
                <span class="text-3xl font-bold text-gray-900">${{ pkg.price }}</span>
                <span class="text-gray-500">/{{ $t('month') }}</span>
              </div>
              <p class="text-sm text-gray-600 mb-4">{{ pkg.description }}</p>
              
              <!-- Key Features -->
              <div class="text-left space-y-2">
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ pkg.unlimited_neighbors ? $t('unlimited_neighbors') : pkg.max_neighbors + ' ' + $t('neighbors') }}
                </div>
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ pkg.unlimited_storage ? $t('unlimited_storage') : pkg.storage_limit_gb + 'GB ' + $t('storage') }}
                </div>
                <div v-if="pkg.file_attachments_enabled" class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ $t('file_attachments') }}
                </div>
                <div v-if="pkg.advanced_reporting" class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ $t('advanced_reporting') }}
                </div>
              </div>

              <!-- Upgrade/Downgrade Indicator -->
              <div v-if="!pkg.is_current" class="mt-4 pt-4 border-t border-gray-200">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="pkg.is_upgrade ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'"
                >
                  {{ pkg.is_upgrade ? $t('upgrade') : $t('downgrade') }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Billing Cycle Selection -->
        <div v-if="form.selectedPackage" class="mb-6">
          <h4 class="text-sm font-medium text-gray-900 mb-3">{{ $t('billing_cycle') }}</h4>
          <div class="grid grid-cols-2 gap-4">
            <label class="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none" :class="form.billingCycle === 'monthly' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'">
              <input type="radio" value="monthly" v-model="form.billingCycle" class="sr-only">
              <div class="flex flex-1">
                <div class="flex flex-col">
                  <span class="block text-sm font-medium text-gray-900">{{ $t('monthly') }}</span>
                  <span class="mt-1 flex items-center text-sm text-gray-500">
                    ${{ form.selectedPackage.price }}/{{ $t('month') }}
                  </span>
                </div>
              </div>
            </label>
            <label class="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none" :class="form.billingCycle === 'annual' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'">
              <input type="radio" value="annual" v-model="form.billingCycle" class="sr-only">
              <div class="flex flex-1">
                <div class="flex flex-col">
                  <span class="block text-sm font-medium text-gray-900">{{ $t('annual') }}</span>
                  <span class="mt-1 flex items-center text-sm text-gray-500">
                    ${{ form.selectedPackage.annual_price }}/{{ $t('year') }}
                    <span v-if="form.selectedPackage.annual_savings_percentage > 0" class="ml-2 text-green-600 font-medium">
                      {{ form.selectedPackage.annual_savings_percentage }}% {{ $t('off') }}
                    </span>
                  </span>
                </div>
              </div>
            </label>
          </div>
        </div>

        <!-- Payment Method Selection -->
        <div v-if="form.selectedPackage" class="mb-6">
          <h4 class="text-sm font-medium text-gray-900 mb-3">{{ $t('payment_method') }}</h4>
          <div class="grid grid-cols-3 gap-4">
            <label v-for="method in paymentMethods" :key="method.value" class="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none" :class="form.paymentMethod === method.value ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'">
              <input type="radio" :value="method.value" v-model="form.paymentMethod" class="sr-only">
              <div class="flex flex-1 items-center">
                <div class="text-sm">
                  <span class="block font-medium text-gray-900">{{ method.label }}</span>
                </div>
              </div>
            </label>
          </div>
        </div>

        <!-- Banking Information (for Bank Transfer) -->
        <div v-if="form.selectedPackage && form.paymentMethod === 'bank_transfer'" class="mb-6">
          <div class="bg-blue-50 border border-blue-200 p-6 rounded-lg">
            <h4 class="font-medium text-blue-900 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
              {{ $t('banking_information') }}
            </h4>
            <div class="bg-white p-4 rounded-md border border-blue-200">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span class="text-sm font-medium text-gray-600">{{ $t('bank_name') }}:</span>
                  <p class="text-sm text-gray-900 font-medium mt-1">Arab Bank</p>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-600">{{ $t('account_holder') }}:</span>
                  <p class="text-sm text-gray-900 font-medium mt-1">Taymoor Qanadilou</p>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-600">iBuraq:</span>
                  <p class="text-sm text-gray-900 font-mono font-medium mt-1">**********</p>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-600">IBAN:</span>
                  <p class="text-sm text-gray-900 font-mono font-medium mt-1">*****************************</p>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-600">{{ $t('swift_code') }}:</span>
                  <p class="text-sm text-gray-900 font-mono font-medium mt-1">ARABPS22XXX</p>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-600">{{ $t('bank_address') }}:</span>
                  <p class="text-sm text-gray-900 mt-1">Nablus Branch<br>Palestine</p>
                </div>
              </div>
              <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                  <div>
                    <h5 class="text-sm font-medium text-yellow-800">{{ $t('payment_instructions') }}</h5>
                    <p class="text-sm text-yellow-700 mt-1">
                      {{ $t('bank_transfer_instructions') }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="py-8">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex">
            <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">{{ $t('error_loading_packages') }}</h3>
              <p class="mt-1 text-sm text-red-700">{{ error }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end pt-4 border-t border-gray-200 space-x-3">
        <button
          @click="$emit('close')"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          {{ $t('cancel') }}
        </button>
        <button
          v-if="form.selectedPackage"
          @click="submitPackageChange"
          :disabled="submitting"
          class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          <span v-if="submitting">{{ $t('processing') }}...</span>
          <span v-else>{{ $t('confirm_change') }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import smartModalMixin from '../mixins/smartModalMixin.js';

export default {
  name: 'PackageUpgradeModal',
  mixins: [smartModalMixin],
  props: {
    currentPackage: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'package-changed'],
  data() {
    return {
      loading: true,
      error: null,
      availablePackages: [],
      form: {
        selectedPackage: null,
        billingCycle: 'monthly',
        paymentMethod: 'credit_card'
      },
      submitting: false,
      paymentMethods: [
        //{ value: 'credit_card', label: this.$t('credit_card') },
        { value: 'bank_transfer', label: this.$t('bank_transfer') },
        { value: 'cash', label: this.$t('cash') }
      ]
    }
  },
  async mounted() {
    await this.loadAvailablePackages()
    // Initialize form tracking
    this.$nextTick(() => {
      this.initializeFormTracking();
    });
  },
  methods: {
    async loadAvailablePackages() {
      try {
        this.loading = true
        this.error = null

        const response = await this.$axios.get('/admin/package/available')
        this.availablePackages = response.data.packages
      } catch (error) {
        console.error('Error loading packages:', error)
        this.error = error.response?.data?.message || 'Failed to load available packages'
      } finally {
        this.loading = false
      }
    },
    
    selectPackage(pkg) {
      this.form.selectedPackage = pkg
    },
    
    async submitPackageChange() {
      if (!this.form.selectedPackage) return

      try {
        this.submitting = true

        await this.$axios.post('/admin/package/change', {
          package_id: this.form.selectedPackage.id,
          billing_cycle: this.form.billingCycle,
          payment_method: this.form.paymentMethod
        })

        this.$emit('package-changed')

        // Show success message
        this.$toast.success(this.$t('package_change_requested'))
        this.handleFormSuccess()
      } catch (error) {
        console.error('Error changing package:', error)
        this.$toast.error(error.response?.data?.message || this.$t('package_change_failed'))
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

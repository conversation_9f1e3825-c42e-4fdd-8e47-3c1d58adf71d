<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\EmailVerificationController;
use App\Http\Controllers\API\AdminInvitationController;
use App\Http\Controllers\API\ExpenseTypeController;
use App\Http\Controllers\API\ExpenseController;
use App\Http\Controllers\API\ExpenseTemplateController;
use App\Http\Controllers\API\BuildingExpenseTypeController;
use App\Http\Controllers\API\BuildingExpenseController;
use App\Http\Controllers\API\IncomeController;
use App\Http\Controllers\API\PaymentController;
use App\Http\Controllers\API\BuildingController;
use App\Http\Controllers\API\NotificationController;
use App\Http\Controllers\API\NotificationTemplateController;
use App\Http\Controllers\API\FileController;
use App\Http\Controllers\API\FileFolderController;
use App\Http\Controllers\API\SearchController;
use App\Http\Controllers\API\UserPreferencesController;
use App\Http\Controllers\API\PackageController;
use App\Http\Controllers\API\SubscriptionController;
use App\Http\Controllers\API\ExportController;
use App\Http\Controllers\API\EmailUsageController;
use App\Http\Controllers\API\ArchiveController;
use App\Http\Controllers\API\AdminManagementController;
use App\Http\Controllers\API\AdvancedReportingController;
use App\Http\Controllers\API\CurrencyController;
use App\Http\Controllers\API\SmsController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\API\WebhookController;
use App\Http\Controllers\Admin\PackageManagementController;
use App\Http\Controllers\Admin\SuperAdminSubscriptionController;
use App\Http\Controllers\Admin\SuperAdminPackageController;
use App\Http\Controllers\Admin\SuperAdminPackageApprovalController;
use App\Http\Controllers\Admin\SuperAdminExpenseTypeController;
use App\Http\Controllers\Admin\SuperAdminBuildingExpenseTypeController;
use App\Models\PackageChangeRequest;
use App\Mail\PackageUpgradeNotificationMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/signup', [AuthController::class, 'signup']);
Route::post('/login', [AuthController::class, 'login']);

// Public email verification route
Route::post('/email/verify', [EmailVerificationController::class, 'verify']);

// Public admin invitation routes
Route::get('/admin/invitation', [AdminInvitationController::class, 'show']);
Route::post('/admin/invitation/accept', [AdminInvitationController::class, 'accept']);

// Public package routes for pricing page
Route::get('/public/packages', [PackageController::class, 'publicIndex']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);

    // Email verification routes
    Route::prefix('email')->group(function () {
        Route::post('/verification-notification', [EmailVerificationController::class, 'send']);
        Route::post('/resend', [EmailVerificationController::class, 'resend']);
        Route::get('/verification-status', [EmailVerificationController::class, 'status']);
    });

    // User preferences routes
    Route::prefix('preferences')->group(function () {
        Route::get('/email', [UserPreferencesController::class, 'getEmailPreferences']);
        Route::put('/email', [UserPreferencesController::class, 'updateEmailPreferences']);
        Route::post('/email/reset', [UserPreferencesController::class, 'resetEmailPreferences']);
        Route::post('/email/test', [UserPreferencesController::class, 'testEmailNotification']);
        Route::get('/email/stats', [UserPreferencesController::class, 'getEmailStats']);
        Route::get('/all', [UserPreferencesController::class, 'getAllPreferences']);
    });

    // Package routes
    Route::prefix('packages')->group(function () {
        Route::get('/', [PackageController::class, 'index']);
        Route::get('/{package}', [PackageController::class, 'show']);
        Route::post('/compare', [PackageController::class, 'compare']);
        Route::post('/recommendations', [PackageController::class, 'recommendations']);
    });

    // Subscription routes
    Route::prefix('subscription')->group(function () {
        Route::get('/current', [SubscriptionController::class, 'current']);
        Route::post('/subscribe', [SubscriptionController::class, 'subscribe']);
        Route::put('/upgrade', [SubscriptionController::class, 'upgrade']);
        Route::post('/cancel', [SubscriptionController::class, 'cancel']);
        Route::post('/renew', [SubscriptionController::class, 'renew']);
        Route::get('/history', [SubscriptionController::class, 'history']);
        Route::get('/usage', [SubscriptionController::class, 'usage']);
        Route::post('/check-limits', [SubscriptionController::class, 'checkLimits']);
    });

    // Expense Types Routes (Admin only)
    Route::apiResource('expense-types', ExpenseTypeController::class)->middleware('admin');

    // Expenses Routes (Admin only for CUD operations, authenticated for read)
    Route::get('expenses', [ExpenseController::class, 'index']);
    Route::get('expenses/monthly', [ExpenseController::class, 'getMonthlyExpenses']);
    Route::get('expenses/summary', [ExpenseController::class, 'getExpenseSummary']);
    Route::get('expenses/totals', [ExpenseController::class, 'getExpenseTotals']);
    Route::get('expenses/{expense}', [ExpenseController::class, 'show']);
    Route::middleware('admin')->group(function () {
        Route::post('expenses', [ExpenseController::class, 'store']);
        Route::put('expenses/{expense}', [ExpenseController::class, 'update']);
        Route::delete('expenses/{expense}', [ExpenseController::class, 'destroy']);
        Route::post('expenses/generate-monthly', [ExpenseController::class, 'generateMonthlyExpenses']);
        Route::post('subscriptions/generate-monthly', [ExpenseController::class, 'generateMonthlySubscriptions']);
    });

    // Building Expense Types Routes (Admin only)
    Route::apiResource('building-expense-types', BuildingExpenseTypeController::class)->middleware('admin');

    // Building Expenses Routes (Admin only for CUD operations, authenticated for read)
    Route::get('building-expenses', [BuildingExpenseController::class, 'index']);
    Route::get('building-expenses/{buildingExpense}', [BuildingExpenseController::class, 'show']);
    Route::get('building-expenses/monthly', [BuildingExpenseController::class, 'getMonthlyBuildingExpenses']);
    Route::get('building-expenses/summary', [BuildingExpenseController::class, 'getBuildingExpenseSummary']);
    Route::middleware('admin')->group(function () {
        Route::post('building-expenses', [BuildingExpenseController::class, 'store']);
        Route::put('building-expenses/{buildingExpense}', [BuildingExpenseController::class, 'update']);
        Route::delete('building-expenses/{buildingExpense}', [BuildingExpenseController::class, 'destroy']);
    });

    // Expense Template routes (super admin only)
    Route::prefix('expense-templates')->middleware(['super_admin'])->group(function () {
        Route::get('/', [ExpenseTemplateController::class, 'index']);
        Route::post('/', [ExpenseTemplateController::class, 'store']);
        Route::get('/expense-types', [ExpenseTemplateController::class, 'getExpenseTypes']);
        Route::get('/recurrence-types', [ExpenseTemplateController::class, 'getRecurrenceTypes']);
        Route::post('/defaults/create', [ExpenseTemplateController::class, 'createDefaults']);
        Route::post('/auto-generate', [ExpenseTemplateController::class, 'runAutoGeneration']);
        Route::get('/{template}', [ExpenseTemplateController::class, 'show']);
        Route::put('/{template}', [ExpenseTemplateController::class, 'update']);
        Route::delete('/{template}', [ExpenseTemplateController::class, 'destroy']);
        Route::post('/{template}/generate', [ExpenseTemplateController::class, 'generateExpenses']);
    });

    // Incomes Routes (Admin only for CUD operations, authenticated for read)
    Route::get('incomes', [IncomeController::class, 'index']);
    Route::get('incomes/summary', [IncomeController::class, 'getSummary']);
    Route::get('incomes/totals', [IncomeController::class, 'getIncomeTotals']);
    Route::get('incomes/{income}', [IncomeController::class, 'show']);
    Route::middleware('admin')->group(function () {
        Route::post('incomes', [IncomeController::class, 'store']);
        Route::put('incomes/{income}', [IncomeController::class, 'update']);
        Route::delete('incomes/{income}', [IncomeController::class, 'destroy']);
    });

    // Buildings Routes
    Route::apiResource('buildings', BuildingController::class)->middleware('super_admin');

    // Super Admin Management Routes
    Route::prefix('super-admin')->middleware('super_admin')->group(function () {
        // Subscription management
        Route::get('/subscriptions', [SuperAdminSubscriptionController::class, 'index']);
        Route::get('/subscriptions/statistics', [SuperAdminSubscriptionController::class, 'statistics']);
        Route::post('/subscriptions', [SuperAdminSubscriptionController::class, 'store']);
        Route::get('/subscriptions/{subscription}', [SuperAdminSubscriptionController::class, 'show']);
        Route::put('/subscriptions/{subscription}', [SuperAdminSubscriptionController::class, 'update']);
        Route::post('/subscriptions/{subscription}/cancel', [SuperAdminSubscriptionController::class, 'cancel']);
        Route::post('/subscriptions/{subscription}/renew', [SuperAdminSubscriptionController::class, 'renew']);

        // Package management
        Route::get('/packages', [SuperAdminPackageController::class, 'index']);
        Route::post('/packages', [SuperAdminPackageController::class, 'store']);
        Route::get('/packages/{package}', [SuperAdminPackageController::class, 'show']);
        Route::put('/packages/{package}', [SuperAdminPackageController::class, 'update']);
        Route::delete('/packages/{package}', [SuperAdminPackageController::class, 'destroy']);
        Route::post('/packages/{package}/toggle-status', [SuperAdminPackageController::class, 'toggleStatus']);
        Route::post('/packages/{package}/duplicate', [SuperAdminPackageController::class, 'duplicate']);

        // Expense Type management
        Route::get('/expense-types', [SuperAdminExpenseTypeController::class, 'index']);
        Route::post('/expense-types', [SuperAdminExpenseTypeController::class, 'store']);
        Route::get('/expense-types/statistics', [SuperAdminExpenseTypeController::class, 'statistics']);
        Route::get('/expense-types/{expenseType}', [SuperAdminExpenseTypeController::class, 'show']);
        Route::put('/expense-types/{expenseType}', [SuperAdminExpenseTypeController::class, 'update']);
        Route::delete('/expense-types/{expenseType}', [SuperAdminExpenseTypeController::class, 'destroy']);
        Route::post('/expense-types/bulk-delete', [SuperAdminExpenseTypeController::class, 'bulkDestroy']);

        // Building Expense Type management
        Route::get('/building-expense-types', [SuperAdminBuildingExpenseTypeController::class, 'index']);
        Route::post('/building-expense-types', [SuperAdminBuildingExpenseTypeController::class, 'store']);
        Route::get('/building-expense-types/statistics', [SuperAdminBuildingExpenseTypeController::class, 'statistics']);
        Route::get('/building-expense-types/{buildingExpenseType}', [SuperAdminBuildingExpenseTypeController::class, 'show']);
        Route::put('/building-expense-types/{buildingExpenseType}', [SuperAdminBuildingExpenseTypeController::class, 'update']);
        Route::delete('/building-expense-types/{buildingExpenseType}', [SuperAdminBuildingExpenseTypeController::class, 'destroy']);
        Route::post('/building-expense-types/bulk-delete', [SuperAdminBuildingExpenseTypeController::class, 'bulkDestroy']);
    });

    // Admin building management
    Route::middleware('admin')->group(function () {
        Route::get('/my-building', [BuildingController::class, 'getMyBuilding']);
        Route::put('/my-building', [BuildingController::class, 'updateMyBuilding']);
    });

    // Payments Routes (Admin only for CUD operations, authenticated for read)
    Route::get('payments', [PaymentController::class, 'index']);
    Route::get('payments/{payment}', [PaymentController::class, 'show']);
    Route::get('payments/user', [PaymentController::class, 'getUserPayments']);
    Route::get('payments/expense', [PaymentController::class, 'getExpensePayments']);
    Route::get('payments/summary', [PaymentController::class, 'getPaymentSummary']);

    // Payment gateway routes (accessible without admin middleware for signup)
    Route::post('payments/setup-fee', [PaymentController::class, 'createSetupFeePayment']);
    Route::get('payments/success', [PaymentController::class, 'paymentSuccess']);
    Route::get('payments/cancel', [PaymentController::class, 'paymentCancel']);
    Route::get('payments/gateways', [PaymentController::class, 'getAvailableGateways']);

    Route::middleware('admin')->group(function () {
        Route::post('payments', [PaymentController::class, 'store']);
        Route::put('payments/{payment}', [PaymentController::class, 'update']);
        Route::delete('payments/{payment}', [PaymentController::class, 'destroy']);
    });

    // Admin routes
    Route::prefix('admin')->middleware('admin')->group(function () {
        Route::get('/users', [UserController::class, 'index']);
    });

    // Admin routes for user management (with building scope check in controller)
    Route::prefix('admin')->middleware(['admin', 'building.access', 'track.admin.activity'])->group(function () {
        Route::post('/users', [UserController::class, 'store'])->middleware(['package.limits:neighbors', 'package.limits:admins']);
        Route::put('/users/{user}', [UserController::class, 'update'])->middleware('package.limits:admins');
        Route::delete('/users/{user}', [UserController::class, 'destroy']);

        // Admin invitation routes
        Route::get('/invitations', [AdminInvitationController::class, 'index']);
        Route::post('/invitations', [AdminInvitationController::class, 'store']);
        Route::post('/invitations/{invitation}/cancel', [AdminInvitationController::class, 'cancel']);
        Route::post('/invitations/{invitation}/resend', [AdminInvitationController::class, 'resend']);

        // Super admin only routes
        Route::middleware('super_admin')->group(function () {
            Route::post('/users/{user}/transfer', [UserController::class, 'transferAdmin']);
        });
    });

    // Package Management Routes (Admin only)
    Route::prefix('admin/package')->middleware('admin')->group(function () {
        Route::get('/current', [PackageManagementController::class, 'getCurrentPackage']);
        Route::get('/available', [PackageManagementController::class, 'getAvailablePackages']);
        Route::get('/storage-usage', [PackageManagementController::class, 'getStorageUsage']);
        Route::get('/restrictions', [PackageManagementController::class, 'getPackageRestrictions']);
        Route::post('/change', [PackageManagementController::class, 'requestPackageChange']);
        Route::get('/pending-requests', [PackageManagementController::class, 'getPendingRequests']);
    });

    // Notification routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::get('/unread-count', [NotificationController::class, 'unreadCount']);
        Route::get('/{notification}', [NotificationController::class, 'show']);
        Route::put('/{notification}/mark-as-read', [NotificationController::class, 'markAsRead']);
        Route::put('/{notification}/mark-as-unread', [NotificationController::class, 'markAsUnread']);
        Route::put('/mark-all-as-read', [NotificationController::class, 'markAllAsRead']);
        Route::delete('/{notification}', [NotificationController::class, 'destroy']);

        // Admin only routes for creating notifications
        Route::middleware('admin')->group(function () {
            Route::post('/', [NotificationController::class, 'store']);
        });
    });

    // Notification Template routes (admin and super admin)
    Route::prefix('notification-templates')->middleware(['admin'])->group(function () {
        Route::get('/', [NotificationTemplateController::class, 'index']);
        Route::post('/', [NotificationTemplateController::class, 'store']);
        Route::get('/defaults', [NotificationTemplateController::class, 'getDefaults']);
        Route::post('/defaults/create', [NotificationTemplateController::class, 'createDefaults']);
        Route::get('/stats', [NotificationTemplateController::class, 'getStats']);
        Route::get('/{template}', [NotificationTemplateController::class, 'show']);
        Route::put('/{template}', [NotificationTemplateController::class, 'update']);
        Route::delete('/{template}', [NotificationTemplateController::class, 'destroy']);
        Route::post('/{template}/create-notifications', [NotificationTemplateController::class, 'createNotifications']);
        Route::post('/{template}/preview', [NotificationTemplateController::class, 'preview']);
    });

    // Payment Gateway Settings routes (super admin only)
    Route::prefix('payment-gateway-settings')->middleware(['super_admin'])->group(function () {
        Route::get('/', [PaymentController::class, 'getPaymentGatewaySettings']);
        Route::put('/', [PaymentController::class, 'updatePaymentGatewaySettings']);
        Route::post('/test', [PaymentController::class, 'testPaymentGatewayConnection']);
    });

    // File upload routes (with package limits)
    Route::prefix('files')->group(function () {
        Route::get('/config', [FileController::class, 'getConfig']);
        Route::post('/upload', [FileController::class, 'upload'])->middleware('package.limits:file_attachments,storage');
        Route::get('/attachments', [FileController::class, 'getAttachments']);
        Route::get('/{fileAttachment}', [FileController::class, 'show']);
        Route::get('/{fileAttachment}/download', [FileController::class, 'download'])->name('files.download');
        Route::put('/{fileAttachment}', [FileController::class, 'update']);
        Route::delete('/{fileAttachment}', [FileController::class, 'destroy']);
    });

    // File Folder routes (admin only)
    Route::prefix('file-folders')->middleware(['admin'])->group(function () {
        Route::get('/', [FileFolderController::class, 'index']);
        Route::post('/', [FileFolderController::class, 'store']);
        Route::get('/tree', [FileFolderController::class, 'getTree']);
        Route::post('/defaults/create', [FileFolderController::class, 'createDefaults']);
        Route::get('/{folder}', [FileFolderController::class, 'show']);
        Route::put('/{folder}', [FileFolderController::class, 'update']);
        Route::delete('/{folder}', [FileFolderController::class, 'destroy']);
        Route::get('/{folder}/files', [FileFolderController::class, 'getFiles']);
        Route::post('/{folder}/move-files', [FileFolderController::class, 'moveFiles']);
    });

    // Search routes
    Route::prefix('search')->group(function () {
        Route::get('/', [SearchController::class, 'search']);
        Route::get('/suggestions', [SearchController::class, 'suggestions']);
    });

    // Export routes (super admin only)
    Route::prefix('exports')->middleware('admin')->group(function () {
        Route::get('/types', [ExportController::class, 'getAvailableTypes']);
        Route::post('/create', [ExportController::class, 'createExport'])->name('api.exports.create');
        Route::get('/', [ExportController::class, 'getExports']);
        Route::get('/stats', [ExportController::class, 'getExportStats']);
        Route::get('/{exportId}', [ExportController::class, 'getExport']);
        Route::get('/{exportId}/download', [ExportController::class, 'downloadExport']);
    });



    // Email usage routes (admin only)
    Route::prefix('email-usage')->middleware('admin')->group(function () {
        Route::get('/stats', [EmailUsageController::class, 'getUsageStats']);
        Route::get('/quota', [EmailUsageController::class, 'getQuotaStatus']);
        Route::get('/history', [EmailUsageController::class, 'getUsageHistory']);
        Route::get('/summary', [EmailUsageController::class, 'getUsageSummary']);
    });

    // Archive routes (admin only, Premium package feature)
    Route::prefix('archive')->middleware(['super_admin', 'track.admin.activity'])->group(function () {
        Route::get('/stats', [ArchiveController::class, 'stats']);
        Route::get('/expenses', [ArchiveController::class, 'getArchivedExpenses']);
        Route::get('/incomes', [ArchiveController::class, 'getArchivedIncomes']);
        Route::post('/expenses', [ArchiveController::class, 'archiveExpenses']);
        Route::post('/incomes', [ArchiveController::class, 'archiveIncomes']);
        Route::post('/expenses/unarchive', [ArchiveController::class, 'unarchiveExpenses']);
        Route::post('/incomes/unarchive', [ArchiveController::class, 'unarchiveIncomes']);
        Route::post('/auto-archive', [ArchiveController::class, 'autoArchive']);
    });

    // Admin Management routes (admin only, Standard+ package feature)
    Route::prefix('admin-management')->middleware(['super_admin', 'track.admin.activity'])->group(function () {
        Route::get('/stats', [AdminManagementController::class, 'getAdminStats']);
        Route::get('/admins', [AdminManagementController::class, 'getAdmins']);
        Route::put('/admins/{admin}/level', [AdminManagementController::class, 'updateAdminLevel']);
        Route::put('/admins/{admin}/permissions', [AdminManagementController::class, 'updateAdminPermissions']);
        Route::put('/admins/{admin}/status', [AdminManagementController::class, 'toggleAdminStatus']);
        Route::get('/activity', [AdminManagementController::class, 'getAdminActivity']);
        Route::get('/activity/summary', [AdminManagementController::class, 'getActivitySummary']);
        Route::get('/permissions', [AdminManagementController::class, 'getAvailablePermissions']);
        Route::get('/limits', [AdminManagementController::class, 'checkAdminLimits']);
    });

    // Enhanced Financial Reporting routes (admin only)
    Route::middleware(['admin'])->group(function () {
        Route::get('/admin/financial-summary', [AdminManagementController::class, 'getFinancialSummary']);
        Route::get('/admin/neighbor-financial-summary', [AdminManagementController::class, 'getNeighborFinancialSummary']);
    });

    // Webhook routes (no authentication required)
    Route::prefix('webhooks')->group(function () {
        Route::post('/paypal', [WebhookController::class, 'handlePayPalWebhook']);
        Route::post('/stripe', [WebhookController::class, 'handleStripeWebhook']);
        Route::post('/amazon', [WebhookController::class, 'handleAmazonWebhook']);
    });

    // Advanced Reporting routes (admin only, Standard+ package feature)
    Route::prefix('advanced-reporting')->middleware(['admin', 'track.admin.activity'])->group(function () {
        Route::get('/stats', [App\Http\Controllers\API\AdvancedReportingController::class, 'getReportingStats']);
        Route::get('/templates', [App\Http\Controllers\API\AdvancedReportingController::class, 'getTemplates']);
        Route::get('/templates/{template}', [App\Http\Controllers\API\AdvancedReportingController::class, 'getTemplate']);
        Route::get('/reports', [App\Http\Controllers\API\AdvancedReportingController::class, 'getCustomReports']);
        Route::post('/reports', [App\Http\Controllers\API\AdvancedReportingController::class, 'createCustomReport']);
        Route::get('/reports/{report}', [App\Http\Controllers\API\AdvancedReportingController::class, 'getCustomReport']);
        Route::put('/reports/{report}', [App\Http\Controllers\API\AdvancedReportingController::class, 'updateCustomReport']);
        Route::delete('/reports/{report}', [App\Http\Controllers\API\AdvancedReportingController::class, 'deleteCustomReport']);
        Route::post('/reports/{report}/generate', [App\Http\Controllers\API\AdvancedReportingController::class, 'generateReportData']);
        Route::get('/reports/{report}/download', [App\Http\Controllers\API\AdvancedReportingController::class, 'downloadReport']);
        Route::get('/analytics', [App\Http\Controllers\API\AdvancedReportingController::class, 'getReportAnalytics']);
        Route::post('/quick-generate', [App\Http\Controllers\API\AdvancedReportingController::class, 'quickGenerate']);
    });

    // Currency Management routes (authenticated users)
    Route::prefix('currency')->middleware(['auth:sanctum', 'track.admin.activity'])->group(function () {
        Route::get('/supported', [CurrencyController::class, 'getSupportedCurrencies']);
        Route::get('/settings', [CurrencyController::class, 'getBuildingCurrencySettings']);
        Route::put('/settings', [CurrencyController::class, 'updateBuildingCurrencySettings']);
        Route::get('/exchange-rates', [CurrencyController::class, 'getExchangeRates']);
        Route::post('/convert', [CurrencyController::class, 'convertAmount']);
        Route::get('/conversion-stats', [CurrencyController::class, 'getConversionStats']);
        Route::post('/exchange-rates', [CurrencyController::class, 'updateExchangeRates']);
        Route::get('/historical-rates', [CurrencyController::class, 'getHistoricalRates']);
        Route::post('/format', [CurrencyController::class, 'formatAmount']);
    });

    // SMS Management routes (authenticated users)
    Route::prefix('sms')->middleware(['auth:sanctum', 'track.admin.activity'])->group(function () {
        Route::get('/settings', [SmsController::class, 'getSmsSettings']);
        Route::put('/settings', [SmsController::class, 'updateSmsSettings']);
        Route::get('/user-preferences', [SmsController::class, 'getUserSmsPreferences']);
        Route::put('/user-preferences', [SmsController::class, 'updateUserSmsPreferences']);
        Route::post('/bulk-send', [SmsController::class, 'sendBulkSms']);
        Route::get('/templates', [SmsController::class, 'getSmsTemplates']);
        Route::post('/templates', [SmsController::class, 'createSmsTemplate']);
        Route::get('/delivery-logs', [SmsController::class, 'getDeliveryLogs']);
        Route::get('/statistics', [SmsController::class, 'getSmsStatistics']);
        Route::post('/test', [SmsController::class, 'testSmsConfiguration']);
    });

});

// Super Admin Package Approval Routes
Route::prefix('super-admin/package-approvals')->middleware(['auth:sanctum', 'super_admin'])->group(function () {
    Route::get('/', [SuperAdminPackageApprovalController::class, 'index']);
    Route::get('/statistics', [SuperAdminPackageApprovalController::class, 'statistics']);
    Route::get('/{packageRequest}', [SuperAdminPackageApprovalController::class, 'show']);
    Route::post('/{packageRequest}/approve', [SuperAdminPackageApprovalController::class, 'approve']);
    Route::post('/{packageRequest}/reject', [SuperAdminPackageApprovalController::class, 'reject']);
});

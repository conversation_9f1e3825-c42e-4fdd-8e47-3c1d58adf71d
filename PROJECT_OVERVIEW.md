# Amaretna — Project Overview

Welcome to <PERSON><PERSON>na (عمارتنا), a building committee management system that helps manage expenses, incomes, residents, and operational workflows with a modern web app and optional mobile builds.

## What this project provides
- Financial management: track incomes, expenses, balance, and generate reports
- Admin controls across roles (e.g., Super Admin, Admin, Neighbor)
- Advanced reporting with customizable fields and PDF exports
- PWA setup for installable web app experience
- Optional mobile builds via Capacitor (Android/iOS)

## Tech stack at a glance
- Backend: Laravel (PHP)
- Frontend: Vite + modern JS framework (Vue 3) and Tailwind CSS
- PWA: Vite build + service worker + web manifest
- Mobile: Capacitor with Android/iOS projects
- Testing: PHP Unit tests, Vitest for frontend, Playwright for E2E

## Get started
For full setup steps (PHP/Node prerequisites, environment setup, migrations, build/run): see README.md.

Key scripts (see package.json):
- Development: `npm run dev`
- Build: `npm run build`
- PWA utilities: `npm run test-pwa`, `npm run debug-sw`, `npm run fix-refresh`
- Tests: `npm run test`, `npm run test:coverage`, `npm run test:e2e`

## Production and deployment
Production hardening and deployment notes are in PRODUCTION_SETUP.md. It covers environment, build artifacts, and web server basics.

## PWA specifics
PWA-README.md provides guidance for icons, manifest, service worker, and verifying PWA behavior.

## Documentation map
Below is a quick map to existing documentation, each with a short description. Open any file for the full details.

- README.md — Main setup, configuration, usage, default accounts, and contribution guide
- PWA-README.md — How the PWA is configured, built, and debugged
- PRODUCTION_SETUP.md — Production build and deployment guidance
- TEST_RESULTS.md — Summary of current test coverage and execution status
- FRONTEND_FEATURES_DOCUMENTATION.md — Overview of frontend features and UX flows
- ADVANCED_REPORTING_GUIDE.md — How to use the Advanced Reporting section and generate PDFs
- SIMPLE_ADVANCED_REPORTING.md — A simplified quick-start for Advanced Reporting
- CREATE_REPORT_FIXES.md — Notes on fixes made to the custom report creation flow (validation, UX)
- EXPENSE_TYPE_MANAGEMENT.md — Super Admin CRUD for expense types and building expense types
- SUBSCRIPTION_IMPROVEMENTS.md — Subscription-related enhancements and flows
- PACKAGE_MANAGEMENT_CLEANUP.md — Cleanup and rationalization of package/dependency management
- PACKAGE_CHANGE_REQUESTS_IMPLEMENTATION.md — How package change requests are implemented

If a file above is missing in your local copy, it may have been renamed or removed in your branch.

## High-level architecture
- Laravel API serves authenticated routes and handles business logic (auth, finance, reports, exports)
- Frontend app (Vite-built) consumes API endpoints and renders admin/neighbor flows
- Service worker + manifest enable installable PWA with offline-friendly behavior
- Capacitor wraps the web app for native Android/iOS deployment where needed

## Testing and quality
- Backend: phpunit driven tests (see tests/)
- Frontend: Vitest unit tests for components and utilities
- E2E: Playwright scenarios

See TEST_RESULTS.md for the latest status and any version/platform notes.

## Contributing
- Follow the guidelines in README.md (coding standards, branching, and PR flow)
- Keep docs in sync with changes; add or update a focused .md in the repo when you add features

## Need deeper pages per topic?
This overview links to all current topic docs. If you want separate, deeper explanations for each area (e.g., detailed architecture, data model, or API endpoints), let us know which sections you want expanded and we’ll add dedicated pages under a docs/ directory.


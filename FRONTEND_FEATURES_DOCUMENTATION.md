# Frontend Features Documentation - عمارتنا (Amaretna)

## Overview
This document provides a comprehensive overview of all frontend features available in the عمارتنا (Amaretna) building management system. The application is built with Vue.js and supports both Arabic and English languages with RTL/LTR support.

## Table of Contents
1. [Public Pages (Guest Users)](#public-pages-guest-users)
2. [Authentication System](#authentication-system)
3. [Admin Features](#admin-features)
4. [Neighbor Features](#neighbor-features)
5. [Shared Features](#shared-features)
6. [Mobile Features](#mobile-features)

---

## Public Pages (Guest Users)

### 1. Home Page (`/`)
**Link:** [Home](/)
**Description:** Landing page showcasing the platform's features and benefits
**Features:**
- Hero section with call-to-action buttons
- Feature highlights and benefits
- Statistics and testimonials
- Responsive design with modern UI
- Language toggle (Arabic/English)
- Navigation to other public pages

### 2. About Page (`/about`)
**Link:** [About](/about)
**Description:** Information about the company and platform
**Features:**
- Company mission and vision
- Team information
- Platform benefits and features
- Call-to-action for getting started
- Responsive layout with engaging visuals

### 3. Services Page (`/services`)
**Link:** [Services](/services)
**Description:** Detailed overview of all services offered
**Features:**
- Comprehensive service listings
- Core services breakdown (6+ services)
- 24/7 support information
- Cloud-based platform benefits
- Contact sales integration

### 4. Pricing Page (`/pricing`)
**Link:** [Pricing](/pricing)
**Description:** Package pricing and subscription plans
**Features:**
- Multiple subscription packages
- Monthly/Annual billing toggle
- Package comparison features
- Setup fee and subscription pricing information
- Feature comparison between packages
- Call-to-action for signup

### 5. Contact Page (`/contact`)
**Link:** [Contact](/contact)
**Description:** Contact information and support channels
**Features:**
- Contact form for inquiries
- Phone support: +962 78 291 2391
- Email support: <EMAIL>
- 24/7 availability information
- Multiple contact methods
- Responsive contact form

### 6. Terms and Conditions (`/terms-and-conditions`)
**Link:** [Terms and Conditions](/terms-and-conditions)
**Description:** Legal terms and conditions for using the platform
**Features:**
- Comprehensive legal documentation
- Last updated information
- Contact information for legal inquiries
- Governing law information
- User rights and responsibilities

---

## Authentication System

### 1. Login Page (`/login`)
**Link:** [Login](/login)
**Description:** User authentication portal
**Features:**
- Email and password authentication
- Remember me functionality
- Role-based redirection (Admin → `/admin`, Neighbor → `/neighbor`)
- Error handling and validation
- Responsive design
- Language support

### 2. Register Page (`/register`)
**Link:** [Register](/register)
**Description:** New user registration
**Features:**
- User registration form
- Email verification process
- Automatic token generation
- Redirect to registration success
- Form validation and error handling

### 3. Sign Up Page (`/signup`)
**Link:** [Sign Up](/signup)
**Description:** Complete building and admin account setup
**Features:**
- Building information setup
- Admin account creation
- Payment integration
- Building details (name, address, city, postal code)
- Monthly fee configuration
- Currency selection (default: JOD)
- Terms and conditions acceptance

### 4. Email Verification (`/verify-email`)
**Link:** [Verify Email](/verify-email)
**Description:** Email verification process
**Features:**
- Email verification workflow
- Resend verification email
- Account activation process

---

## Admin Features

### 1. Admin Dashboard (`/admin`)
**Link:** [Admin Dashboard](/admin)
**Description:** Main administrative control panel
**Features:**
- Financial overview and statistics
- Total expenses, income, and balance tracking
- User and building statistics
- Interactive charts and graphs
- Monthly comparison data
- Advanced financial reporting
- Time range filtering
- Export capabilities

### 2. Expense Management (`/admin/expenses`)
**Link:** [Expense Management](/admin/expenses)
**Description:** Complete expense tracking and management
**Features:**
- View all building expenses
- Create, edit, and delete expenses
- Monthly expense generation
- Automatic subscription expense generation
- Expense filtering by type, month, year
- Pagination support
- Expense templates
- Bulk operations

### 3. Income Management (`/admin/incomes`)
**Link:** [Income Management](/admin/incomes)
**Description:** Income and payment tracking
**Features:**
- Record and track all payments
- Income filtering and search
- Monthly income statistics
- Average income calculations
- Payment date tracking
- Income categorization
- Export functionality

### 4. User Management (`/admin/users`)
**Link:** [User Management](/admin/users)
**Description:** Manage building residents and users
**Features:**
- View all neighbors in building
- Create new user accounts
- Edit user information
- Delete user accounts
- User role management
- Building assignment
- User statistics and filtering

### 5. Building Management (`/admin/buildings`)
**Link:** [Building Management](/admin/buildings)
**Description:** Building information and settings management
**Features:**
- Building profile management
- Building settings configuration
- Monthly fee management
- Currency settings
- Building statistics
- Address and contact information

### 6. Advanced Reporting (`/admin/advanced-reporting`)
**Link:** [Advanced Reporting](/admin/advanced-reporting)
**Description:** Comprehensive reporting and analytics
**Features:**
- Financial reports and analytics
- Custom date range reporting
- Export to various formats
- Comparative analysis
- Trend analysis
- Visual charts and graphs

### 7. SMS Management (`/admin/sms`)
**Link:** [SMS Management](/admin/sms)
**Description:** SMS notification system management
**Features:**
- Send SMS notifications
- SMS templates
- Bulk SMS sending
- SMS history and tracking
- SMS settings configuration

### 8. Export Management (`/admin/export`)
**Link:** [Export Management](/admin/export)
**Description:** Data export and backup functionality
**Features:**
- Export financial data
- Export user data
- Multiple export formats
- Scheduled exports
- Data backup options

---

## Super Admin Features (Additional)

### 1. Subscription Management (`/super-admin/subscriptions`)
**Link:** [Subscription Management](/super-admin/subscriptions)
**Description:** Manage all building subscriptions
**Features:**
- View all building subscriptions
- Subscription status management
- Payment tracking
- Subscription renewals
- Package assignments

### 2. Package Management (`/super-admin/packages`)
**Link:** [Package Management](/super-admin/packages)
**Description:** Manage subscription packages and features
**Features:**
- Create and edit packages
- Feature management
- Pricing configuration
- Package limitations
- Feature toggles

### 3. Notification Templates (`/admin/notification-templates`)
**Link:** [Notification Templates](/admin/notification-templates)
**Description:** Manage system notification templates
**Features:**
- Email templates
- SMS templates
- Template customization
- Multi-language support
- Template preview

### 4. Payment Gateway Settings (`/admin/payment-gateway-settings`)
**Link:** [Payment Gateway Settings](/admin/payment-gateway-settings)
**Description:** Configure payment processing
**Features:**
- Payment gateway configuration
- Payment method settings
- Transaction management
- Payment security settings

---

## Neighbor Features

### 1. Neighbor Dashboard (`/neighbor`)
**Link:** [Neighbor Dashboard](/neighbor)
**Description:** Personal dashboard for residents
**Features:**
- Outstanding balance display
- Personal expense history
- Payment records
- Financial summary
- Color-coded balance indicators (red for debt, green for credit)

### 2. Profile Management (`/neighbor/profile`)
**Link:** [Profile Management](/neighbor/profile)
**Description:** Personal profile and account settings
**Features:**
- Update personal information
- Change email address
- Profile picture management
- Contact information updates
- Account settings

---

## Shared Features

### 1. Notification Center
**Description:** Real-time notification system
**Features:**
- Real-time notifications
- Unread notification counter
- Notification history
- Mark as read functionality
- Notification filtering
- Mobile-optimized notifications

### 2. Search Functionality (`/search`)
**Link:** [Search Results](/search)
**Description:** Global search across the platform
**Features:**
- Global search with suggestions
- Real-time search results
- Search history
- Advanced filtering
- Quick access to search results

### 3. Language Toggle
**Description:** Multi-language support system
**Features:**
- Arabic/English language switching
- RTL/LTR layout support
- Persistent language preference
- Complete UI translation
- Cultural localization

---

## Mobile Features

### 1. Mobile Navigation
**Description:** Mobile-optimized navigation system
**Features:**
- Bottom navigation bar (PWA style)
- Touch-friendly interface
- Swipe gestures
- Mobile-specific layouts
- Responsive design

### 2. Progressive Web App (PWA)
**Description:** Mobile app-like experience
**Features:**
- Install prompt
- Offline functionality
- Push notifications
- App-like navigation
- Mobile optimization

---

## Technical Features

### 1. Responsive Design
- Mobile-first approach
- Tablet and desktop optimization
- Touch-friendly interfaces
- Adaptive layouts

### 2. Real-time Updates
- Live data synchronization
- Real-time notifications
- Automatic data refresh
- WebSocket integration

### 3. Security Features
- Laravel Sanctum token authentication
- Role-based access control
- Secure API communication
- Session management

### 4. Performance Optimization
- Lazy loading
- Code splitting
- Optimized assets
- Fast loading times

---

## Contact Information
- **Email:** <EMAIL>
- **Phone:** +962 78 291 2391
- **Support:** 24/7 availability
- **Website:** [عمارتنا Platform](/)

---

*Last Updated: July 27, 2025*

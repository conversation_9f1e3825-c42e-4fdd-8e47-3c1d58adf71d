{"name": "عمارتنا - Amaretna - Building Committee Management", "short_name": "عمارتنا - <PERSON><PERSON><PERSON>", "description": "Complete building committee management system for expenses, incomes, and neighbor management", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#2563eb", "orientation": "portrait-primary", "scope": "/", "lang": "ar", "dir": "rtl", "categories": ["business", "finance", "productivity"], "icons": [{"src": "/build/assets/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any maskable"}, {"src": "/build/assets/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any maskable"}, {"src": "/build/assets/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any maskable"}, {"src": "/build/assets/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "/build/assets/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "/build/assets/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/build/assets/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "/build/assets/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}, {"src": "/favicon.ico", "sizes": "64x64 32x32 24x24 16x16", "type": "image/x-icon"}], "shortcuts": [{"name": "Dashboard", "short_name": "Dashboard", "description": "Go to main dashboard", "url": "/admin"}, {"name": "Add Expense", "short_name": "Add Expense", "description": "Add new expense", "url": "/admin/expenses?action=add"}, {"name": "View Payments", "short_name": "Payments", "description": "View payment history", "url": "/neighbor"}], "related_applications": [{"platform": "webapp", "url": "https://amaretna.com/manifest.json"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "protocol_handlers": [{"protocol": "web+amaretna", "url": "/?handler=%s"}]}
{"name": "عمارتنا - <PERSON><PERSON><PERSON>", "$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "node generate-pwa-icons.js && vite build && cp public/build/.vite/manifest.json public/build/manifest.json", "dev": "vite", "generate-icons": "node generate-pwa-icons.js", "test-pwa": "node test-pwa-setup.js", "debug-sw": "node debug-service-worker.js", "fix-refresh": "node fix-refresh-issues.js", "demo-super-admin": "php demo-super-admin-bypass.php", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug"}, "devDependencies": {"@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/vite": "^4.0.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.5", "tailwindcss": "^4.1.10", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^6.2.4"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/cli": "^6.2.1", "@capacitor/core": "^7.4.2", "@capacitor/ios": "^7.4.2", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vitejs/plugin-vue": "^5.2.4", "chart.js": "^4.5.0", "vue": "^3.5.16", "vue-router": "^4.5.1"}}
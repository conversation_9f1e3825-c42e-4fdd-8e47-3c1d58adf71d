<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Expense;
use App\Services\QueryOptimizationService;
use App\Services\CachingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Artisan;

class ExpenseController extends Controller
{
    protected QueryOptimizationService $queryOptimization;
    protected CachingService $caching;

    public function __construct(QueryOptimizationService $queryOptimization, CachingService $caching)
    {
        $this->queryOptimization = $queryOptimization;
        $this->caching = $caching;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        // Determine building ID for query
        $buildingId = $user->role === 'super_admin' && $request->filled('building_id')
            ? $request->integer('building_id')
            : $user->building_id;

        // Prepare filters for optimized query
        $filters = [
            'is_archived' => $request->boolean('include_archived') ? null : false,
            'month' => $request->filled('month') ? $request->integer('month') : null,
            'year' => $request->filled('year') ? $request->integer('year') : null,
            'user_id' => $request->filled('user_id') ? $request->integer('user_id') : null,
            'expense_type_id' => null, // Will be handled separately if needed
        ];

        // Remove null filters
        $filters = array_filter($filters, fn($value) => $value !== null);

        // Use optimized query
        $query = $this->queryOptimization->getOptimizedExpensesQuery($buildingId, $filters);

        // Handle expense type filter separately for better performance
        if ($request->filled('type')) {
            $query->whereHas('expenseType', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->type . '%');
            });
        }

        // Handle automatic expense filter
        if ($request->filled('is_automatic')) {
            $query->where('is_automatic', $request->boolean('is_automatic'));
        }

        // Paginate results with optimized page size
        $perPage = min($request->integer('per_page', 25), 100); // Max 100 items per page
        $expenses = $query->paginate($perPage);

        return response()->json($expenses);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'expense_type_id' => 'required|exists:expense_types,id',
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'month' => 'required|string|size:2',
            'year' => 'required|integer|min:2020|max:2030',
            'notes' => 'nullable|string',
            'is_automatic' => 'boolean',
        ]);

        // Auto-set building_id from logged-in user
        $validated['building_id'] = $user->building_id;

        // Validate that the selected user belongs to the same building (unless super admin)
        if ($user->role !== 'super_admin') {
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only create expenses for users in your building.'], 403);
            }
        }

        // Convert year to string for storage
        $validated['year'] = (string) $validated['year'];



        // Auto-generate due_date as end of month
        $validated['due_date'] = \Carbon\Carbon::createFromDate($validated['year'], $validated['month'], 1)->endOfMonth();

        $expense = Expense::create($validated);
        $expense->load(['expenseType', 'user']);
        return response()->json($expense, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Expense $expense): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            if ($expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only view expenses in your building.'], 403);
            }
        }

        $expense->load(['expenseType', 'user', 'payments']);
        return response()->json($expense);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expense $expense): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            if ($expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only update expenses in your building.'], 403);
            }
        }

        $validated = $request->validate([
            'expense_type_id' => 'required|exists:expense_types,id',
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'month' => 'required|string|size:2',
            'year' => 'required|integer|min:2020|max:2030',
            'notes' => 'nullable|string',
            'is_automatic' => 'boolean',
        ]);

        // Validate that the selected user belongs to the same building (unless super admin)
        if ($user->role !== 'super_admin') {
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only assign expenses to users in your building.'], 403);
            }
        }

        // Convert year to string for storage
        $validated['year'] = (string) $validated['year'];

        // Keep the original building_id (don't allow changing it unless super admin)
        if ($user->role !== 'super_admin') {
            $validated['building_id'] = $expense->building_id;
        }

        // Auto-generate due_date as end of month if not provided
        if (!isset($validated['due_date'])) {
            $validated['due_date'] = \Carbon\Carbon::createFromDate($validated['year'], $validated['month'], 1)->endOfMonth();
        }

        $expense->update($validated);
        $expense->load(['expenseType', 'user']);
        return response()->json($expense);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, Expense $expense): JsonResponse
    {
        $user = $request->user();

        // Check building scope authorization (unless super admin)
        if ($user->role !== 'super_admin') {
            if ($expense->building_id !== $user->building_id) {
                return response()->json(['message' => 'Unauthorized. You can only delete expenses in your building.'], 403);
            }
        }

        $expense->delete();
        return response()->json(null, 204);
    }

    public function getMonthlyExpenses(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
        ]);

        $query = Expense::with(['expenseType', 'payments'])
            ->where('month', $validated['month'])
            ->where('year', $validated['year'])
            ->active(); // Exclude archived records

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->where('building_id', $user->building_id);
        }

        $expenses = $query->get();
        return response()->json($expenses);
    }

    public function getExpenseSummary(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = Expense::selectRaw('
            expense_type_id,
            month,
            year,
            SUM(amount) as total_amount,
            COUNT(*) as total_expenses,
            COUNT(*) as total_count
        ')
        ->active(); // Exclude archived records

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->where('building_id', $user->building_id);
        }

        $summary = $query->groupBy('expense_type_id', 'month', 'year')
            ->with('expenseType')
            ->get();

        return response()->json($summary);
    }

    public function getExpenseTotals(Request $request): JsonResponse
    {
        $user = $request->user();

        // Determine building ID for query
        $buildingId = $user->role === 'super_admin' && $request->filled('building_id')
            ? $request->integer('building_id')
            : $user->building_id;

        // Prepare filters
        $filters = [
            'month' => $request->filled('month') ? $request->string('month') : null,
            'year' => $request->filled('year') ? $request->integer('year') : null,
            'type' => $request->filled('type') ? $request->string('type') : null,
        ];

        // Base query
        $baseQuery = Expense::active()->where('building_id', $buildingId);

        // Apply filters
        if ($filters['month']) {
            $baseQuery->where('month', $filters['month']);
        }
        if ($filters['year']) {
            $baseQuery->where('year', $filters['year']);
        }
        if ($filters['type']) {
            $baseQuery->whereHas('expenseType', function ($query) use ($filters) {
                $query->where('name', $filters['type']);
            });
        }

        // Calculate totals
        $totalExpenses = (clone $baseQuery)->sum('amount');

        // This month expenses
        $currentMonth = now()->month;
        $currentYear = now()->year;
        $thisMonthExpenses = (clone $baseQuery)
            ->where('month', str_pad($currentMonth, 2, '0', STR_PAD_LEFT))
            ->where('year', $currentYear)
            ->sum('amount');

        // Automatic vs Manual expenses
        $automaticExpenses = (clone $baseQuery)->where('is_automatic', true)->sum('amount');
        $manualExpenses = (clone $baseQuery)->where('is_automatic', false)->sum('amount');

        return response()->json([
            'totalExpenses' => (float) $totalExpenses,
            'thisMonthExpenses' => (float) $thisMonthExpenses,
            'automaticExpenses' => (float) $automaticExpenses,
            'manualExpenses' => (float) $manualExpenses,
        ]);
    }

    public function generateMonthlyExpenses(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'month' => 'required|string|size:2',
            'year' => 'required|string|size:4',
        ]);

        $user = $request->user();
        $commandParams = [
            '--month' => $validated['month'],
            '--year' => $validated['year'],
        ];

        // If user is admin (not super_admin), only generate for their building
        if ($user->role === 'admin' && $user->building_id) {
            $commandParams['--building'] = $user->building_id;
        }

        // Call the artisan command
        Artisan::call('expenses:generate-monthly', $commandParams);

        $output = Artisan::output();

        return response()->json([
            'message' => 'Monthly expenses generation completed',
            'output' => $output
        ]);
    }

    public function generateMonthlySubscriptions(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'month' => 'required|string|size:2',
            'year' => 'required|string|size:4',
        ]);

        $user = $request->user();
        $commandParams = [
            '--month' => $validated['month'],
            '--year' => $validated['year'],
        ];

        // If user is admin (not super_admin), only generate for their building
        if ($user->role === 'admin' && $user->building_id) {
            $commandParams['--building'] = $user->building_id;
        }

        // Call the artisan command
        Artisan::call('subscriptions:generate-monthly', $commandParams);

        $output = Artisan::output();

        return response()->json([
            'message' => 'Monthly subscription expenses generation completed',
            'output' => $output
        ]);
    }
}
